import { z } from "zod";

// Helper to create multilingual error messages
const createErrorMessage = (greekMsg: string, englishMsg: string) =>
  `${greekMsg} / ${englishMsg}`;

// Social Media Platform Options
export const socialMediaPlatformOptions = [
  { id: "facebook", labelEl: "Facebook", labelEn: "Facebook" },
  { id: "instagram", labelEl: "Instagram", labelEn: "Instagram" },
  { id: "linkedin", labelEl: "LinkedIn", labelEn: "LinkedIn" },
  { id: "twitter", labelEl: "Twitter", labelEn: "Twitter" },
  { id: "tiktok", labelEl: "TikTok", labelEn: "TikTok" },
  { id: "other", labelEl: "Άλλο", labelEn: "Other" },
];

// Social Media Schema
export const socialMediaSchema = z.object({
  platform: z.string().min(1, {
    message: createErrorMessage(
      "Παρακαλώ επιλέξτε πλατφόρμα",
      "Please select a platform"
    ),
  }),
  handle: z.string().min(1, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε το όνομα χρήστη/handle",
      "Please enter the username/handle"
    ),
  }),
});

// Nanny Request Form Schemas
export const nannyRequestFamilyInfoSchema = z.object({
  fatherName: z.string().min(2, {
    message: createErrorMessage(
      "Το όνομα πρέπει να έχει τουλάχιστον 2 χαρακτήρες",
      "Name must be at least 2 characters"
    ),
  }),
  motherName: z.string().min(2, {
    message: createErrorMessage(
      "Το όνομα πρέπει να έχει τουλάχιστον 2 χαρακτήρες",
      "Name must be at least 2 characters"
    ),
  }),
  fatherOccupation: z.string().optional(),
  motherOccupation: z.string().optional(),
  contactNumber: z.string().min(10, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε έναν έγκυρο αριθμό τηλεφώνου",
      "Please enter a valid phone number"
    ),
  }),
  email: z.string().email({
    message: createErrorMessage(
      "Παρακαλώ εισάγετε μια έγκυρη διεύθυνση email",
      "Please enter a valid email address"
    ),
  }),
  permanentAddress: z.string().min(5, {
    message: createErrorMessage(
      "Η διεύθυνση πρέπει να έχει τουλάχιστον 5 χαρακτήρες",
      "Address must be at least 5 characters"
    ),
  }),
  city: z.string().min(2, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε την πόλη",
      "Please enter the city"
    ),
  }),
  workingCity: z.string().optional(),
  workingAddressSameAsPermanent: z.boolean().default(false),
  workingAddress: z.string().optional(),
  childrenCount: z.string().optional(),
  childrenAge: z.array(z.string()),
  childSpecialInfo: z.string().optional(),
});

export const nannyRequestPositionSchema = z.object({
  positionType: z.enum(["live-in", "live-out"]),
  positionDuration: z.enum(["long-term", "short-term"]),
  scheduleType: z.enum(["full-time", "part-time"]),
  daysPerWeek: z
    .string()
    .min(1, {
      message: createErrorMessage(
        "Παρακαλώ συμπληρώστε τις ημέρες ανά εβδομάδα",
        "Please fill in days per week"
      ),
    })
    .refine((val) => parseInt(val) <= 7, {
      message: createErrorMessage(
        "Ο μέγιστος αριθμός ημερών ανά εβδομάδα είναι 7",
        "Maximum number of days per week is 7"
      ),
    }),
  hoursPerDay: z
    .string()
    .min(1, {
      message: createErrorMessage(
        "Παρακαλώ συμπληρώστε τις ώρες ανά ημέρα",
        "Please fill in hours per day"
      ),
    })
    .refine((val) => parseInt(val) <= 24, {
      message: createErrorMessage(
        "Ο μέγιστος αριθμός ωρών ανά ημέρα είναι 24",
        "Maximum number of hours per day is 24"
      ),
    }),
  salaryType: z.enum(["monthly", "hourly", "daily", "weekly", "package"]),
  salaryAmount: z.string().min(1, {
    message: createErrorMessage(
      "Παρακαλώ συμπληρώστε το ποσό",
      "Please fill in the amount"
    ),
  }),
  salaryCurrency: z.enum(["EUR", "USD", "GBP"]),
  positionInterests: z.array(z.string()).min(1, {
    message: createErrorMessage(
      "Παρακαλώ επιλέξτε τουλάχιστον μία θέση",
      "Please select at least one position"
    ),
  }),
  specializationPreferences: z.array(z.string()),
  insurance: z.enum(["yes", "no", "maybe"]),
  insuranceType: z.enum(["ergosimo", "stamps", "booklet"]).optional(),
  startDate: z.enum(["immediately", "weeks", "flexible", "other"]),
  startDateSpecific: z.string().optional(),
});

export const nannyRequestPreferencesSchema = z.object({
  candidateAge: z.string().optional(),
  workingLevel: z.enum(["junior", "basic", "vip"]),
  smoker: z.enum(["no", "dont-mind"]),
  drivingLicense: z.enum(["yes", "dont-mind"]),
  vehicle: z.enum(["yes", "dont-mind"]),
  firstAid: z.enum(["yes", "dont-mind"]),
  learningDifficulties: z.enum(["yes", "dont-mind"]),
  specialNeeds: z.enum(["yes", "dont-mind"]),
  nationality: z.string().optional(),
  languages: z
    .array(
      z.object({
        language: z.string(),
        level: z.string(),
      })
    )
    .optional(),
  additionalDetails: z.string().optional(),
  documentUpload: z.any().optional(),
});

export const nannyRequestFormSchema = z.object({
  ...nannyRequestFamilyInfoSchema.shape,
  ...nannyRequestPositionSchema.shape,
  ...nannyRequestPreferencesSchema.shape,
});

export const nannyRequestFormSteps = [
  {
    id: "family-info",
    schema: nannyRequestFamilyInfoSchema,
  },
  {
    id: "position-details",
    schema: nannyRequestPositionSchema,
  },
  {
    id: "candidate-preferences",
    schema: nannyRequestPreferencesSchema,
  },
];

// Step 1: Combined Personal Information and Address Schema
export const personalInfoSchema = z.object({
  name: z.string().min(2, {
    message: createErrorMessage(
      "Το όνομα πρέπει να έχει τουλάχιστον 2 χαρακτήρες",
      "Name must be at least 2 characters"
    ),
  }),
  surname: z.string().min(2, {
    message: createErrorMessage(
      "Το επίθετο πρέπει να έχει τουλάχιστον 2 χαρακτήρες",
      "Surname must be at least 2 characters"
    ),
  }),
  gender: z.enum(["Male", "Female", "Other"]),
  birthDate: z.string().min(1, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε την ημερομηνία γέννησης",
      "Please enter your birth date"
    ),
  }),
  contactNumber: z.string().min(10, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε έναν έγκυρο αριθμό τηλεφώνου",
      "Please enter a valid phone number"
    ),
  }),
  email: z.string().email({
    message: createErrorMessage(
      "Παρακαλώ εισάγετε μια έγκυρη διεύθυνση email",
      "Please enter a valid email address"
    ),
  }),
  nationality: z.string().min(2, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε την εθνικότητα-υπηκοότητα",
      "Please enter the nationality-citizenship"
    ),
  }),
  workDocuments: z.enum(["yes", "no"]),
  // Address fields
  address: z.string().min(2, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε τη διεύθυνση",
      "Please enter the address"
    ),
  }),
  addressNumber: z.string().min(1, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε τον αριθμό",
      "Please enter the number"
    ),
  }),
  area: z.string().min(2, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε την περιοχή",
      "Please enter the area"
    ),
  }),
  postalCode: z.string().min(4, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε έναν έγκυρο ταχυδρομικό κώδικα",
      "Please enter a valid postal code"
    ),
  }),
  city: z.string().min(2, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε την πόλη",
      "Please enter the city"
    ),
  }),
  country: z.string().min(2, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε τη χώρα",
      "Please enter the country"
    ),
  }),
  socialMedia: z.array(socialMediaSchema).optional(),
  allergies: z
    .object({
      hasAllergies: z.enum(["yes", "no"]),
      allergyDetails: z.string().optional(),
    })
    .optional()
    .refine(
      (data) => {
        if (data?.hasAllergies === "yes") {
          return data.allergyDetails && data.allergyDetails.trim().length > 0;
        }
        return true;
      },
      {
        message: createErrorMessage(
          "Παρακαλώ εισάγετε λεπτομέρειες για τις αλλεργίες",
          "Please enter allergy details"
        ),
        path: ["allergyDetails"], // specify the path of the error
      }
    ),
  passportActive: z.enum(["yes", "no"]).optional(),
});

// Combined Career Profile Schema (without emergency contacts and documents)
export const careerProfileSchema = z.object({
  // Experience fields
  drivingLicense: z.enum(["yes", "no"]),
  vehicle: z.enum(["yes", "no"]),
  vehicleType: z.string().optional(),
  smoker: z.enum(["yes", "no"]),
  firstAidCertification: z.array(z.string()),
  firstAidUpdate: z.string().optional(),
  experienceWithChildren: z.enum(["yes", "no"]),
  experienceLearningDifficulties: z.enum(["yes", "no"]),
  experienceSpecialNeeds: z.enum(["yes", "no"]),
  yearsExperience: z.string().min(1, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε τα χρόνια εμπειρίας",
      "Please enter years of experience"
    ),
  }),
  childrenAgeExperience: z.array(z.string()),

  // Languages & Interests fields
  languages: z.array(
    z.object({
      language: z.string(),
      level: z.string(),
    })
  ),
  positionInterests: z.array(z.string()).min(1, {
    message: createErrorMessage(
      "Παρακαλώ επιλέξτε τουλάχιστον μία θέση",
      "Please select at least one position"
    ),
  }),
  scheduleInterests: z.array(z.string()).min(1, {
    message: createErrorMessage(
      "Παρακαλώ επιλέξτε τουλάχιστον ένα ωράριο",
      "Please select at least one schedule type"
    ),
  }),
  durationInterests: z.array(z.string()).min(1, {
    message: createErrorMessage(
      "Παρακαλώ επιλέξτε τουλάχιστον μία διάρκεια",
      "Please select at least one duration"
    ),
  }),
  positionType: z.array(z.string()),
  startAvailability: z.string().optional(),
  placePreferences: z.string().optional(),

  // Candidate type and tutor-specific fields
  candidateType: z.enum(["nanny", "tutor", "both"]).default("nanny"),
  musicalInstruments: z.array(z.string()).optional(),
  musicTheory: z.array(z.string()).optional(),
  lessonFormat: z.array(z.string()).optional(),

  // Education fields
  education: z.array(z.string()),
  educationTitles: z.array(z.string()).optional(),
  specialization: z.enum(["yes", "no"]),
  specializationTypes: z.array(z.string()).optional(),

  // Additional skills fields
  experiencedDriver: z.enum(["yes", "no"]),
  comfortableWithPets: z.enum(["yes", "no"]),
  experiencedSwimmer: z.enum(["yes", "no"]),
  travelAvailability: z.enum(["yes", "no"]),
  nightShiftAvailability: z.enum(["yes", "no"]),
  hobbies: z.string().optional(),

  // Profile fields
  personalProfile: z.string().min(10, {
    message: createErrorMessage(
      "Παρακαλώ συμπληρώστε το προσωπικό σας προφίλ",
      "Please fill in your personal profile"
    ),
  }),

  // Work experience fields
  workExperience: z.array(
    z.object({
      period: z.string(),
      location: z.string(),
      position: z.string(),
      children: z.string(),
      schedule: z.string(),
      endingReason: z.string(),
    })
  ),

  // References fields
  references: z.enum(["yes", "no"]),
  criminalRecord: z.string().optional(),
  referencesContacts: z.array(
    z.object({
      name: z.string(),
      phone: z.string(),
      email: z.string(),
    })
  ),
});

// Final Step: Emergency Contact & Documents Schema
export const finalStepSchema = z.object({
  // Emergency contact fields
  emergencyContactName: z.string().min(2, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε το όνομα επαφής έκτακτης ανάγκης",
      "Please enter emergency contact name"
    ),
  }),
  emergencyContactRelation: z.string().min(2, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε τη σχέση με την επαφή έκτακτης ανάγκης",
      "Please enter emergency contact relation"
    ),
  }),
  emergencyContactNumber: z.string().min(10, {
    message: createErrorMessage(
      "Παρακαλώ εισάγετε έναν έγκυρο αριθμό τηλεφώνου",
      "Please enter a valid phone number"
    ),
  }),
  emergencyContactEmail: z.string().email({
    message: createErrorMessage(
      "Παρακαλώ εισάγετε μια έγκυρη διεύθυνση email",
      "Please enter a valid email address"
    ),
  }),

  // Documents & Terms fields
  profilePhoto: z.any().optional(),
  introVideo: z.any().optional(),
  educationDocuments: z.any().optional(),
  idPassport: z.any().optional(),
  drivingLicenseDocument: z.any().optional(),
  criminalRecordDocument: z.any().optional(),
  nannyCv: z.any().optional(),
  referenceLetters: z.any().optional(),
  insurance: z.enum(["yes", "no", "maybe"]),
  insuranceType: z.enum(["ergosimo", "stamps", "booklet"]).optional(),
  termsAccepted: z.boolean().refine((val) => val === true, {
    message: createErrorMessage(
      "Πρέπει να αποδεχτείτε τους όρους και προϋποθέσεις",
      "You must accept the terms and conditions"
    ),
  }),
});

// Combined Form Schema
export const formSchema = z.object({
  ...personalInfoSchema.shape,
  ...careerProfileSchema.shape,
  ...finalStepSchema.shape,
});

// Define step configuration
export const formSteps = [
  {
    id: "personal-info",
    schema: personalInfoSchema,
  },
  {
    id: "career-profile",
    schema: careerProfileSchema,
  },
  {
    id: "emergency-documents",
    schema: finalStepSchema,
  },
];

// Export option constants
export const childrenAgeOptions = [
  {
    id: "newborns",
    labelEl: "Νεογέννητα (Ημέρα 1-3 μηνών)",
    labelEn: "Newborns (Day 1- 3 months old)",
  },
  {
    id: "infants_3_9",
    labelEl: "Βρέφη (3-9 μηνών)",
    labelEn: "Infants (3-9 months old)",
  },
  {
    id: "infants_9_18",
    labelEl: "Βρέφη (9-18 μηνών)",
    labelEn: "Infants (9-18 months old)",
  },
  {
    id: "toddlers",
    labelEl: "Νήπια (18 μηνών-3 ετών)",
    labelEn: "Toddlers (18 months-3 years old)",
  },
  {
    id: "preschool",
    labelEl: "Προσχολική (3-6 ετών)",
    labelEn: "Preschool (3-6 years old)",
  },
  {
    id: "primary",
    labelEl: "Δημοτικό (7-12 ετών)",
    labelEn: "Primary school (7-12 years old)",
  },
  {
    id: "teenagers",
    labelEl: "Εφηβεία (13+ ετών)",
    labelEn: "Teenagers (13+years old)",
  },
];

export const startDateOptions = [
  { id: "immediately", labelEl: "Άμεσα", labelEn: "Immediately" },
  { id: "weeks", labelEl: "Σε λίγες εβδομάδες", labelEn: "In a few weeks" },
  { id: "flexible", labelEl: "Ευέλικτα", labelEn: "Flexible" },
  { id: "other", labelEl: "Άλλο", labelEn: "Other" },
];

export const positionOptions = [
  { id: "nanny", label: "Nanny" },
  { id: "maternity_nanny", label: "Maternity/Night Nanny" },
  { id: "travel_nanny", label: "Travel/Vacation Nanny" },
  { id: "hotel_nanny", label: "Hotel Nanny" },
  { id: "yacht_nanny", label: "Yacht Nanny" },
  { id: "event_nanny", label: "Event Nanny" },
  { id: "weekend_nanny", label: "Weekend Nanny" },
  { id: "babysitting", label: "Baby-Sitting" },
];

export const scheduleOptions = [
  { id: "full-time", label: "Full-Time" },
  { id: "part-time", label: "Part-Time" },
  { id: "flexible", label: "Flexible" },
];

export const durationOptions = [
  { id: "long-term", label: "Long-Term" },
  { id: "short-term", label: "Short-Term" },
];

export const candidateTypeOptions = [
  { id: "nanny", label: "Nanny / Nanny" },
  { id: "tutor", label: "Tutor / Tutor" },
  { id: "both", label: "Και τα δύο / Both" },
];

export const musicalInstrumentOptions = [
  { id: "piano", label: "Πιάνο / Piano" },
  { id: "harmonium", label: "Αρμόνιο / Harmonium" },
  { id: "guitar", label: "Κιθάρα / Guitar" },
  { id: "recorder", label: "Φλογέρα / Recorder" },
  { id: "flute", label: "Φλάουτο / Flute" },
  { id: "violin", label: "Βιολί / Violin" },
  { id: "cello", label: "Βιολοντσέλο / Cello" },
  { id: "alto_saxophone", label: "Σαξόφωνο Άλτο / Alto Saxophone" },
  { id: "bouzouki", label: "Μπουζούκι / Bouzouki" },
  {
    id: "traditional_percussion",
    label: "Παραδοσιακά Κρουστά / Traditional Percussion",
  },
  { id: "vocals", label: "Φωνητική / Vocals" },
];

export const musicTheoryOptions = [
  { id: "theory", label: "Θεωρία / Theory" },
  { id: "harmony", label: "Αρμονία / Harmony" },
  { id: "counterpoint", label: "Αντίστιξη / Counterpoint" },
  { id: "fugue", label: "Φούγκα / Fugue" },
  { id: "composition", label: "Σύνθεση / Composition" },
  { id: "jazz_theory", label: "Jazz Θεωρία / Jazz Theory" },
  { id: "byzantine_music", label: "Βυζαντινή Μουσική / Byzantine Music" },
];

export const lessonFormatOptions = [
  { id: "live", label: "Δια ζώσης / Live" },
  { id: "online", label: "Διαδικτυακά / Online" },
];

export const educationOptions = [
  { id: "high_school", label: "Απολυτήριο Λυκείου / High School Certificate" },
  { id: "tei", label: "Τ.Ε.Ι. / Technological Education Institute" },
  { id: "aei", label: "Α.Ε.Ι. / College" },
  { id: "iek", label: "Ι.Ε.Κ. / Institute of Vocational Training" },
  { id: "associate", label: "Πιστοποιημένο Πτυχίο /Associates Degree" },
  { id: "bachelor", label: "Πτυχίο Bachelor / Bachelor's Degree" },
  {
    id: "master",
    label: "Μεταπτυχιακό Δίπλωμα /Master of Science or Master's Degree",
  },
  { id: "phd", label: "Διδακτορικό Δίπλωμα /PhD" },
];

export const specializationOptions = [
  { id: "maternity_nurse", label: "Μαία / Maternity Nurse" },
  { id: "maternity_nanny", label: "Maternity Nanny" },
  {
    id: "nursery_assistant",
    label: "Βοηθός Βρεφονηπιοκόμου / Nursery Childcare Assistant",
  },
  { id: "nursery_childcare", label: "Βρεφονηπιοκόμος / Nursery Childcare" },
  { id: "kindergarten_teacher", label: "Νηπιαγωγός / Kindergarten Teacher" },
  {
    id: "primary_school_teacher",
    label: "Δημοτική Εκπαίδευση / Primary School Teacher",
  },
  {
    id: "pedagogy_general",
    label: "Παιδαγωγικά (Γενικά) / Other Pedagogy Certification",
  },
  { id: "foreign_languages", label: "Ξένες Γλώσσες / Foreign Languages" },
  { id: "sign_language", label: "Νοηματική Γλώσσα / Sign Language" },
  { id: "braille_system", label: "Γραφή Braille / Braille System" },
  {
    id: "special_needs_education",
    label: "Ειδικής Αγωγής / Special Needs Education",
  },
  {
    id: "occupational_therapy",
    label: "Εργοθεραπεία / Occupational Therapy",
  },
  { id: "speech_therapy", label: "Λογοθεραπεία / Speech Therapy" },
  { id: "music_pedagogy", label: "Μουσικοπαιδαγωγικά / Music Pedagogy" },
  { id: "music_therapy", label: "Μουσικοθεραπεία / Music Therapy" },
  { id: "musical_instrument", label: "Μουσικό Όργανο / Musical Instrument" },
  { id: "music_theory", label: "Θεωρητικά της Μουσικής / Music Theory" },
  { id: "theatre_pedagogy", label: "Θεατροπαιδαγωγικά / Theatre Pedagogy" },
  { id: "drama_therapy", label: "Δραματοθεραπεία / Drama Therapy" },
  { id: "play_therapy", label: "Παιγνιοθεραπεία / Play Therapy" },
  { id: "visual_arts", label: "Εικαστικές Τέχνες / Visual Arts" },
  {
    id: "dance_kinesiology",
    label: "Χορός-Κινησιολογία / Dance-Kinesiology",
  },
  {
    id: "dance_movement_therapy",
    label: "Χοροθεραπεία-Χοροκινητική Ψυχοθεραπεία / Dance-Movement Therapy",
  },
  { id: "psychology", label: "Ψυχολογία / Psychology" },
  { id: "child_psychology", label: "Παιδοψυχολογία / Child Psychology" },
  { id: "child_psychiatry", label: "Παιδοψυχιατρική / Child Psychiatry" },

  { id: "other", label: "Κάτι άλλο… / Other…" },
];

export const firstAidOptions = [
  { id: "infants", labelEl: "Για βρέφη", labelEn: "For infants" },
  {
    id: "children",
    labelEl: "Για παιδιά",
    labelEn: "For children",
  },
  {
    id: "adults",
    labelEl: "Για ενήλικες",
    labelEn: "For adults",
  },
];

export type FormData = z.infer<typeof formSchema>;
